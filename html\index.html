<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyFlow - Redirecting...</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">


    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" as="script">

    <!-- Cache manager -->


    <link href="../css/alarm-service.css" rel="stylesheet">




</head>
<body>
    <div class="nav-brand d-flex align-items-center">
        <img
            src="assets/images/gpace-logo-white.png"
            alt="GPAce Logo"
            style="height: 80px; margin-right: 0px;"
        >
        <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
    </div>
    <p>Redirecting to StudyFlow landing page...</p>
    <!-- Scripts -->
    <script src="../js/cacheManager.js"></script>
    <script type="module" src="../js/cross-tab-sync.js"></script>
    <script>
        // Check if page is in cache before redirecting
        if ('caches' in window) {
            caches.match('landing.html')
                .then(response => {
                    if (response) {
                        window.location.href = 'landing.html';
                    } else {
                        // If not in cache, prefetch landing page then redirect
                        const prefetchLink = document.createElement('link');
                        prefetchLink.rel = 'prefetch';
                        prefetchLink.href = 'landing.html';
                        document.head.appendChild(prefetchLink);

                        setTimeout(() => {
                            window.location.href = 'landing.html';
                        }, 100);
                    }
                });
        } else {
            window.location.href = 'landing.html';
        }
    </script>
    <script src="../js/alarm-service.js"></script>
    <script src="../js/alarm-mini-display.js"></script>
    <script src="../js/inject-header.js"></script>
</body>
</html>